from django.contrib.auth.models import AbstractUser
from django.db import models
from django.core.validators import RegexValidator


class User(AbstractUser):
    """
    Custom User model for Mthunzi system with role-based access.

    Roles:
    - Forge: System/brand owner, powers the platform
    - Pod: Business owner using Mthunzi to sell
    - Pulse: Customer interacting via WhatsApp
    - Runner: Delivery agent handling shipments
    """

    class UserRole(models.TextChoices):
        FORGE = 'forge', 'Forge (System Owner)'
        POD = 'pod', 'Pod (Business Owner)'
        PULSE = 'pulse', 'Pulse (Customer)'
        RUNNER = 'runner', 'Runner (Delivery Agent)'

    role = models.CharField(
        max_length=10,
        choices=UserRole.choices,
        default=UserRole.PULSE,
        help_text="User role in the Mthunzi system"
    )

    phone_regex = RegexValidator(
        regex=r'^\+?1?\d{9,15}$',
        message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed."
    )
    phone_number = models.CharField(
        validators=[phone_regex],
        max_length=17,
        unique=True,
        help_text="WhatsApp phone number"
    )

    whatsapp_id = models.CharField(
        max_length=100,
        unique=True,
        null=True,
        blank=True,
        help_text="WhatsApp user ID for API integration"
    )

    is_verified = models.BooleanField(
        default=False,
        help_text="Whether the user's phone number is verified"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.username} ({self.get_role_display()})"

    @property
    def is_forge(self):
        return self.role == self.UserRole.FORGE

    @property
    def is_pod(self):
        return self.role == self.UserRole.POD

    @property
    def is_pulse(self):
        return self.role == self.UserRole.PULSE

    @property
    def is_runner(self):
        return self.role == self.UserRole.RUNNER


class Business(models.Model):
    """
    Business model for Pods (business owners) using the Mthunzi system.
    Supports different business types: Product/Goods, Services, and Food/Restaurant.
    """

    class BusinessType(models.TextChoices):
        PRODUCT_GOODS = 'product_goods', 'Product & Goods'
        SERVICE = 'service', 'Service Business'
        FOOD_RESTAURANT = 'food_restaurant', 'Food & Restaurant'

    class BusinessStatus(models.TextChoices):
        ACTIVE = 'active', 'Active'
        INACTIVE = 'inactive', 'Inactive'
        SUSPENDED = 'suspended', 'Suspended'

    owner = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        limit_choices_to={'role': User.UserRole.POD},
        related_name='business'
    )

    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)

    # Business type and category
    business_type = models.CharField(
        max_length=20,
        choices=BusinessType.choices,
        default=BusinessType.PRODUCT_GOODS,
        help_text="Type of business: Product/Goods, Service, or Food/Restaurant"
    )

    # Business contact information
    business_phone = models.CharField(max_length=17, blank=True)
    business_email = models.EmailField(blank=True)
    address = models.TextField(blank=True)

    # Business settings
    status = models.CharField(
        max_length=10,
        choices=BusinessStatus.choices,
        default=BusinessStatus.ACTIVE
    )

    # WhatsApp Business settings
    whatsapp_business_number = models.CharField(max_length=17, blank=True)
    welcome_message = models.TextField(
        default="Welcome to our business! How can we help you today?",
        help_text="Welcome message sent to new customers"
    )

    # Service business specific settings
    booking_advance_days = models.PositiveIntegerField(
        default=30,
        help_text="How many days in advance customers can book appointments"
    )
    booking_buffer_minutes = models.PositiveIntegerField(
        default=15,
        help_text="Buffer time between appointments in minutes"
    )
    service_hours_start = models.TimeField(
        default='09:00',
        help_text="Business opening time for services"
    )
    service_hours_end = models.TimeField(
        default='17:00',
        help_text="Business closing time for services"
    )

    # Food/Restaurant business specific settings
    daily_menu_enabled = models.BooleanField(
        default=False,
        help_text="Enable daily menu management for food businesses"
    )
    preparation_time_minutes = models.PositiveIntegerField(
        default=30,
        help_text="Average preparation time for food orders in minutes"
    )
    accepts_preorders = models.BooleanField(
        default=True,
        help_text="Whether the business accepts advance orders"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.get_business_type_display()})"

    @property
    def is_product_business(self):
        """Check if this is a product/goods business."""
        return self.business_type == self.BusinessType.PRODUCT_GOODS

    @property
    def is_service_business(self):
        """Check if this is a service business."""
        return self.business_type == self.BusinessType.SERVICE

    @property
    def is_food_business(self):
        """Check if this is a food/restaurant business."""
        return self.business_type == self.BusinessType.FOOD_RESTAURANT

    @property
    def requires_inventory_management(self):
        """Check if this business type requires inventory management."""
        return self.business_type == self.BusinessType.PRODUCT_GOODS

    @property
    def supports_appointments(self):
        """Check if this business type supports appointment booking."""
        return self.business_type == self.BusinessType.SERVICE

    @property
    def supports_daily_menu(self):
        """Check if this business type supports daily menu management."""
        return self.business_type == self.BusinessType.FOOD_RESTAURANT

    class Meta:
        verbose_name_plural = "Businesses"


class UserProfile(models.Model):
    """
    Extended profile information for users.
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')

    # Personal information
    date_of_birth = models.DateField(null=True, blank=True)
    avatar = models.ImageField(upload_to='avatars/', null=True, blank=True)

    # Address information
    street_address = models.CharField(max_length=255, blank=True)
    city = models.CharField(max_length=100, blank=True)
    state = models.CharField(max_length=100, blank=True)
    postal_code = models.CharField(max_length=20, blank=True)
    country = models.CharField(max_length=100, default='South Africa')

    # Preferences
    preferred_language = models.CharField(max_length=10, default='en')
    timezone = models.CharField(max_length=50, default='Africa/Johannesburg')

    # For Runners - additional information
    vehicle_type = models.CharField(
        max_length=50,
        blank=True,
        help_text="Type of vehicle for delivery (for Runners)"
    )
    license_number = models.CharField(
        max_length=50,
        blank=True,
        help_text="Driver's license number (for Runners)"
    )
    is_available = models.BooleanField(
        default=True,
        help_text="Whether the runner is available for deliveries"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username}'s Profile"
