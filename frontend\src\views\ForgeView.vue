<template>
  <div class="min-h-screen bg-gradient-to-br from-[#EDF6FF] via-[#F5F5F5] to-[#FFF1DB]">
    <!-- Header -->
    <header class="bg-[#FFFFFF] shadow-sm border-b border-[#CCCCCC]/30">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo & Title -->
          <div class="flex items-center space-x-4">
            <div class="w-10 h-10 bg-gradient-to-br from-[#1E4E79] to-[#132F4C] rounded-xl flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"/>
              </svg>
            </div>
            <div>
              <h1 class="text-xl font-bold text-[#111111]">Forge Dashboard</h1>
              <p class="text-sm text-[#4B4B4B]">System Management</p>
            </div>
          </div>

          <!-- User Menu -->
          <div class="flex items-center space-x-4">
            <button class="p-2 text-[#4B4B4B] hover:text-[#1E4E79] transition-colors duration-300">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5-5-5h5v-5a7.5 7.5 0 01-7.5-7.5H2.5"/>
              </svg>
            </button>
            <div class="w-8 h-8 bg-gradient-to-br from-[#C1843E] to-[#704A1F] rounded-full flex items-center justify-center">
              <span class="text-white text-sm font-semibold">F</span>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Stats Overview -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-[#FFFFFF] rounded-2xl p-6 shadow-sm border border-[#CCCCCC]/30 hover:shadow-md transition-all duration-300">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-[#4B4B4B]">Total Pods</p>
              <p class="text-3xl font-bold text-[#111111]">{{ stats.totalPods }}</p>
            </div>
            <div class="w-12 h-12 bg-[#1E4E79]/10 rounded-xl flex items-center justify-center">
              <svg class="w-6 h-6 text-[#1E4E79]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-[#FFFFFF] rounded-2xl p-6 shadow-sm border border-[#CCCCCC]/30 hover:shadow-md transition-all duration-300">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-[#4B4B4B]">Active Orders</p>
              <p class="text-3xl font-bold text-[#111111]">{{ stats.activeOrders }}</p>
            </div>
            <div class="w-12 h-12 bg-[#2E7D32]/10 rounded-xl flex items-center justify-center">
              <svg class="w-6 h-6 text-[#2E7D32]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-[#FFFFFF] rounded-2xl p-6 shadow-sm border border-[#CCCCCC]/30 hover:shadow-md transition-all duration-300">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-[#4B4B4B]">Total Revenue</p>
              <p class="text-3xl font-bold text-[#111111]">R{{ stats.totalRevenue.toLocaleString() }}</p>
            </div>
            <div class="w-12 h-12 bg-[#C1843E]/10 rounded-xl flex items-center justify-center">
              <svg class="w-6 h-6 text-[#C1843E]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-[#FFFFFF] rounded-2xl p-6 shadow-sm border border-[#CCCCCC]/30 hover:shadow-md transition-all duration-300">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-[#4B4B4B]">System Health</p>
              <p class="text-3xl font-bold text-[#2E7D32]">{{ stats.systemHealth }}%</p>
            </div>
            <div class="w-12 h-12 bg-[#2E7D32]/10 rounded-xl flex items-center justify-center">
              <svg class="w-6 h-6 text-[#2E7D32]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="mb-8">
        <h2 class="text-lg font-semibold text-[#111111] mb-4">Quick Actions</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button class="bg-[#FFFFFF] rounded-2xl p-6 shadow-sm border border-[#CCCCCC]/30 hover:shadow-md transition-all duration-300 text-left group">
            <div class="flex items-center space-x-4">
              <div class="w-12 h-12 bg-[#1E4E79]/10 rounded-xl flex items-center justify-center group-hover:bg-[#1E4E79] transition-colors duration-300">
                <svg class="w-6 h-6 text-[#1E4E79] group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                </svg>
              </div>
              <div>
                <h3 class="font-semibold text-[#111111]">Add New Pod</h3>
                <p class="text-sm text-[#4B4B4B]">Register a new business</p>
              </div>
            </div>
          </button>

          <button class="bg-[#FFFFFF] rounded-2xl p-6 shadow-sm border border-[#CCCCCC]/30 hover:shadow-md transition-all duration-300 text-left group">
            <div class="flex items-center space-x-4">
              <div class="w-12 h-12 bg-[#C1843E]/10 rounded-xl flex items-center justify-center group-hover:bg-[#C1843E] transition-colors duration-300">
                <svg class="w-6 h-6 text-[#C1843E] group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                </svg>
              </div>
              <div>
                <h3 class="font-semibold text-[#111111]">View Analytics</h3>
                <p class="text-sm text-[#4B4B4B]">System-wide reports</p>
              </div>
            </div>
          </button>

          <button class="bg-[#FFFFFF] rounded-2xl p-6 shadow-sm border border-[#CCCCCC]/30 hover:shadow-md transition-all duration-300 text-left group">
            <div class="flex items-center space-x-4">
              <div class="w-12 h-12 bg-[#132F4C]/10 rounded-xl flex items-center justify-center group-hover:bg-[#132F4C] transition-colors duration-300">
                <svg class="w-6 h-6 text-[#132F4C] group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                </svg>
              </div>
              <div>
                <h3 class="font-semibold text-[#111111]">System Settings</h3>
                <p class="text-sm text-[#4B4B4B]">Configure platform</p>
              </div>
            </div>
          </button>
        </div>
      </div>

      <!-- Pods Management -->
      <div class="bg-[#FFFFFF] rounded-2xl shadow-sm border border-[#CCCCCC]/30">
        <div class="p-6 border-b border-[#CCCCCC]/30">
          <div class="flex justify-between items-center">
            <h2 class="text-lg font-semibold text-[#111111]">Registered Pods</h2>
            <button class="bg-gradient-to-r from-[#1E4E79] to-[#132F4C] text-white px-4 py-2 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:scale-105">
              Add Pod
            </button>
          </div>
        </div>
        
        <div class="p-6">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead>
                <tr class="border-b border-[#CCCCCC]/30">
                  <th class="text-left py-3 px-4 font-semibold text-[#111111]">Business Name</th>
                  <th class="text-left py-3 px-4 font-semibold text-[#111111]">Owner</th>
                  <th class="text-left py-3 px-4 font-semibold text-[#111111]">Status</th>
                  <th class="text-left py-3 px-4 font-semibold text-[#111111]">Orders</th>
                  <th class="text-left py-3 px-4 font-semibold text-[#111111]">Revenue</th>
                  <th class="text-left py-3 px-4 font-semibold text-[#111111]">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="pod in pods" :key="pod.id" class="border-b border-[#CCCCCC]/20 hover:bg-[#F5F5F5] transition-colors duration-200">
                  <td class="py-4 px-4">
                    <div class="flex items-center space-x-3">
                      <div class="w-10 h-10 bg-gradient-to-br from-[#C1843E] to-[#704A1F] rounded-xl flex items-center justify-center">
                        <span class="text-white font-semibold text-sm">{{ pod.name.charAt(0) }}</span>
                      </div>
                      <div>
                        <p class="font-medium text-[#111111]">{{ pod.name }}</p>
                        <p class="text-sm text-[#4B4B4B]">{{ pod.category }}</p>
                      </div>
                    </div>
                  </td>
                  <td class="py-4 px-4 text-[#111111]">{{ pod.owner }}</td>
                  <td class="py-4 px-4">
                    <span :class="getStatusClass(pod.status)" class="px-3 py-1 rounded-full text-xs font-medium">
                      {{ pod.status }}
                    </span>
                  </td>
                  <td class="py-4 px-4 text-[#111111]">{{ pod.orders }}</td>
                  <td class="py-4 px-4 text-[#111111]">R{{ pod.revenue.toLocaleString() }}</td>
                  <td class="py-4 px-4">
                    <div class="flex space-x-2">
                      <button class="p-2 text-[#1E4E79] hover:bg-[#1E4E79]/10 rounded-lg transition-colors duration-200">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                        </svg>
                      </button>
                      <button class="p-2 text-[#C1843E] hover:bg-[#C1843E]/10 rounded-lg transition-colors duration-200">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// Reactive data
const stats = ref({
  totalPods: 24,
  activeOrders: 156,
  totalRevenue: 89750,
  systemHealth: 98
})

const pods = ref([
  {
    id: 1,
    name: 'Fresh Groceries',
    category: 'Food & Beverages',
    owner: 'John Doe',
    status: 'Active',
    orders: 45,
    revenue: 12500
  },
  {
    id: 2,
    name: 'Tech Solutions',
    category: 'Electronics',
    owner: 'Jane Smith',
    status: 'Active',
    orders: 23,
    revenue: 34200
  },
  {
    id: 3,
    name: 'Fashion Hub',
    category: 'Clothing',
    owner: 'Mike Johnson',
    status: 'Pending',
    orders: 12,
    revenue: 8900
  }
])

// Methods
const getStatusClass = (status) => {
  switch (status) {
    case 'Active':
      return 'bg-[#2E7D32]/10 text-[#2E7D32]'
    case 'Pending':
      return 'bg-[#FF8F00]/10 text-[#FF8F00]'
    case 'Inactive':
      return 'bg-[#C62828]/10 text-[#C62828]'
    default:
      return 'bg-[#4B4B4B]/10 text-[#4B4B4B]'
  }
}

onMounted(() => {
  // Load data on component mount
  console.log('Forge Dashboard loaded')
})
</script>
