"""
URL patterns for the accounts app.
"""

from django.urls import path
from . import views, dashboard_views

app_name = 'accounts'

urlpatterns = [
    # Authentication endpoints
    path('register/', views.UserRegistrationView.as_view(), name='register'),
    path('login/', views.UserLoginView.as_view(), name='login'),
    path('logout/', views.UserLogoutView.as_view(), name='logout'),
    
    # User profile endpoints
    path('profile/', views.UserProfileView.as_view(), name='profile'),
    path('profile/detail/', views.UserProfileDetailView.as_view(), name='profile-detail'),
    path('password/change/', views.PasswordChangeView.as_view(), name='password-change'),
    path('phone/verify/', views.PhoneVerificationView.as_view(), name='phone-verify'),
    
    # Business endpoints
    path('businesses/', views.BusinessListCreateView.as_view(), name='business-list'),
    path('businesses/<int:pk>/', views.BusinessDetailView.as_view(), name='business-detail'),
    path('businesses/<int:business_id>/capabilities/', views.business_capabilities, name='business-capabilities'),
    path('capabilities/', views.business_capabilities, name='my-business-capabilities'),
    
    # User management endpoints
    path('users/', views.UserListView.as_view(), name='user-list'),
    path('runners/', views.RunnerListView.as_view(), name='runner-list'),

    # Dashboard endpoints
    path('dashboard/', views.business_dashboard, name='business-dashboard'),
    path('dashboard/business/', dashboard_views.business_dashboard, name='business-dashboard-detailed'),
    path('dashboard/sales/', dashboard_views.sales_analytics, name='sales-analytics'),
    path('dashboard/system/', dashboard_views.system_dashboard, name='system-dashboard'),
    path('dashboard/inventory/', dashboard_views.inventory_alerts, name='inventory-alerts'),
    path('dashboard/activity/', dashboard_views.user_activity, name='user-activity'),
]
