# Business Setup Guide - Mthunzi Platform

## Overview

The Mthunzi platform supports three types of businesses, each with specialized features designed for their unique needs:

1. **Product/Goods Businesses** - Retail stores, shops, e-commerce
2. **Service Businesses** - Salons, consultants, repair services, healthcare
3. **Food/Restaurant Businesses** - Restaurants, cafes, food trucks, catering

## Getting Started

### 1. Business Registration

When registering your business, you'll need to specify your business type. This determines which features and tools are available to you.

**Required Information:**
- Business name and description
- Contact information (phone, email, address)
- Business type selection
- WhatsApp Business number (optional but recommended)

### 2. Business Type Selection

Choose the type that best describes your business:

#### Product/Goods Business
**Best for:** Retail stores, online shops, distributors, wholesalers
**Features:**
- Product catalog management
- Inventory tracking and alerts
- Order management with delivery
- Stock level monitoring
- WhatsApp ordering system

#### Service Business
**Best for:** Salons, spas, consultants, repair services, healthcare providers, fitness trainers
**Features:**
- Service catalog management
- Appointment booking system
- Schedule management
- Customer appointment history
- WhatsApp appointment booking

#### Food/Restaurant Business
**Best for:** Restaurants, cafes, food trucks, catering services, bakeries
**Features:**
- Daily menu management
- Per-order availability tracking
- Preparation time management
- Special offers and pricing
- WhatsApp food ordering

## Product/Goods Business Setup

### Step 1: Configure Business Settings
- Set your business hours
- Configure delivery zones (if applicable)
- Set up payment methods

### Step 2: Add Products
1. Go to Products section
2. Click "Add Product"
3. Fill in product details:
   - Name and description
   - Price and SKU
   - Stock quantity
   - Low stock threshold
   - Product images
   - Category

### Step 3: Inventory Management
- Set up low stock alerts
- Configure automatic reorder points
- Track stock movements
- Generate inventory reports

### Step 4: Order Management
- Configure order statuses
- Set up delivery options
- Train staff on order processing
- Set up WhatsApp ordering

**Example Product Setup:**
```
Product: "Men's T-Shirt"
SKU: "TSHIRT-M-001"
Price: R150.00
Stock: 50 units
Low Stock Alert: 10 units
Category: "Clothing"
```

## Service Business Setup

### Step 1: Configure Service Settings
- Set booking advance period (e.g., 30 days)
- Configure buffer time between appointments
- Set business hours for services
- Define cancellation policy

### Step 2: Create Services
1. Go to Services section
2. Click "Add Service"
3. Fill in service details:
   - Service name and description
   - Duration (in minutes)
   - Price
   - Category
   - Deposit requirements (if any)
   - Buffer time after service

### Step 3: Set Service Availability
- Configure weekly schedule for each service
- Set different hours for different days
- Block out unavailable times
- Set up recurring availability patterns

### Step 4: Appointment Management
- Train staff on appointment confirmation
- Set up appointment reminders
- Configure cancellation procedures
- Enable WhatsApp booking

**Example Service Setup:**
```
Service: "Haircut & Styling"
Duration: 60 minutes
Price: R250.00
Buffer Time: 15 minutes
Availability: Mon-Sat, 9:00 AM - 5:00 PM
Deposit Required: R50.00
```

## Food/Restaurant Business Setup

### Step 1: Configure Food Business Settings
- Set average preparation time
- Enable daily menu management
- Configure pre-order acceptance
- Set business hours

### Step 2: Create Base Menu Items
1. Go to Products section
2. Add your regular menu items:
   - Dish name and description
   - Base price
   - Category (appetizers, mains, desserts)
   - Dietary information
   - Images

### Step 3: Daily Menu Management
1. Go to Daily Menus section
2. Create today's menu:
   - Select available items
   - Set quantities for each item
   - Apply special pricing
   - Add daily notes or specials

### Step 4: Order Processing
- Set up kitchen display system
- Configure order status updates
- Train staff on preparation tracking
- Enable WhatsApp food ordering

**Example Daily Menu Setup:**
```
Date: Today
Preparation Time: 25 minutes

Menu Items:
- Burger Special: 30 available, R45.00 (special price)
- Fish & Chips: 20 available, R65.00
- Vegetarian Pasta: 15 available, R55.00

Daily Note: "Fresh fish delivered this morning!"
```

## WhatsApp Integration Setup

### 1. Connect WhatsApp Business
- Verify your WhatsApp Business number
- Set up business profile
- Configure automated responses

### 2. Customize Welcome Message
Tailor your welcome message based on business type:

**Product Business:**
"Welcome to [Business Name]! 🛍️ Type 'menu' to see our products or 'order' to place an order."

**Service Business:**
"Welcome to [Business Name]! 💇‍♀️ Type 'services' to see what we offer or 'book' to schedule an appointment."

**Food Business:**
"Welcome to [Business Name]! 🍽️ Type 'menu' to see today's specials or 'order' to place your food order."

### 3. Train Your Team
- Teach staff WhatsApp command responses
- Set up notification preferences
- Configure business hours for auto-responses

## Dashboard and Analytics

### Product Business Dashboard
- Sales overview (daily, weekly, monthly)
- Top-selling products
- Low stock alerts
- Order status summary
- Revenue trends

### Service Business Dashboard
- Appointment overview
- Service popularity
- Revenue by service
- Customer retention metrics
- Schedule utilization

### Food Business Dashboard
- Daily sales summary
- Popular menu items
- Preparation time analytics
- Revenue trends
- Menu item performance

## Best Practices

### For All Business Types
1. **Keep information updated** - Regular updates to products/services/menus
2. **Respond promptly** - Quick responses to WhatsApp messages
3. **Monitor analytics** - Use dashboard data to make informed decisions
4. **Train your team** - Ensure all staff understand the system

### Product Businesses
- Set realistic stock levels
- Update inventory regularly
- Use high-quality product images
- Organize products into clear categories

### Service Businesses
- Keep schedules up to date
- Confirm appointments promptly
- Send appointment reminders
- Maintain service quality standards

### Food Businesses
- Update daily menus regularly
- Manage quantities accurately
- Communicate preparation times clearly
- Highlight daily specials

## Troubleshooting

### Common Issues

**"I can't add products to my service business"**
- Service businesses use services, not products
- Go to Services section instead of Products

**"Daily menu option is missing"**
- Only food businesses have daily menus
- Check your business type in settings

**"WhatsApp commands not working"**
- Verify your business type is set correctly
- Check that WhatsApp number is verified
- Ensure business is active

### Getting Help
- Check the FAQ section
- Contact support via WhatsApp
- Use the help command in your dashboard
- Join the business owner community forum

## Next Steps

After setup:
1. **Test your system** - Place test orders/bookings
2. **Train your team** - Ensure everyone knows how to use the platform
3. **Promote your WhatsApp** - Share your WhatsApp number with customers
4. **Monitor performance** - Use analytics to optimize your business
5. **Gather feedback** - Ask customers about their experience

## Support Contacts

- **Technical Support:** <EMAIL>
- **WhatsApp Support:** +27 XX XXX XXXX
- **Business Hours:** Monday - Friday, 8:00 AM - 6:00 PM
- **Emergency Support:** Available 24/7 for critical issues
